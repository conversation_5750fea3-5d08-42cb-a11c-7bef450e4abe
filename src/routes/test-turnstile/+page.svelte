<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';

  let turnstileContainer: HTMLDivElement;
  let widgetId: string | null = null;
  let isLoaded = false;
  let token = '';
  let verificationResult = '';

  const SITE_KEY = import.meta.env.PUBLIC_TURNSTILE_SITE_KEY;

  const loadTurnstile = () => {
    return new Promise<void>((resolve, reject) => {
      if (typeof window.turnstile !== 'undefined') {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
      script.async = true;
      script.defer = true;
      
      script.onload = () => {
        let attempts = 0;
        const checkTurnstile = () => {
          if (typeof window.turnstile !== 'undefined') {
            resolve();
          } else if (attempts < 50) {
            attempts++;
            setTimeout(checkTurnstile, 100);
          } else {
            reject(new Error('Turnstile API not available'));
          }
        };
        checkTurnstile();
      };
      
      script.onerror = () => reject(new Error('Failed to load Turnstile script'));
      document.head.appendChild(script);
    });
  };

  const handleSuccess = async (receivedToken: string) => {
    console.log('Turnstile success! Token:', receivedToken);
    token = receivedToken;
  };

  const handleError = (error: string) => {
    console.error('Turnstile error:', error);
    verificationResult = 'Error: ' + error;
  };

  const renderTurnstile = async () => {
    if (!browser || !turnstileContainer || !SITE_KEY) {
      console.error('Cannot render Turnstile');
      return;
    }
    
    try {
      await loadTurnstile();
      
      if (widgetId) {
        window.turnstile.remove(widgetId);
      }

      widgetId = window.turnstile.render(turnstileContainer, {
        sitekey: SITE_KEY,
        theme: 'light',
        size: 'normal',
        callback: handleSuccess,
        'error-callback': handleError
      });
      
      isLoaded = true;
    } catch (error) {
      console.error('Error loading Turnstile:', error);
      verificationResult = 'Load error: ' + (error as Error).message;
    }
  };

  onMount(() => {
    renderTurnstile();
  });

  declare global {
    interface Window {
      turnstile: any;
    }
  }
</script>

<svelte:head>
  <title>Turnstile Test - SourceFlex</title>
</svelte:head>

<div class="container mx-auto p-8 max-w-2xl">
  <h1 class="text-3xl font-bold mb-6">Turnstile Test Page</h1>
  
  <div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <h2 class="text-xl font-semibold mb-4">Configuration</h2>
    <div class="bg-gray-100 p-4 rounded">
      <p><strong>Site Key:</strong> {SITE_KEY}</p>
      <p><strong>Is Browser:</strong> {browser}</p>
      <p><strong>Widget Loaded:</strong> {isLoaded}</p>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <h2 class="text-xl font-semibold mb-4">Turnstile Widget</h2>
    <div bind:this={turnstileContainer} class="flex justify-center"></div>
  </div>

  {#if token}
    <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
      <h2 class="text-xl font-semibold text-green-800 mb-4">Success!</h2>
      <p class="text-sm text-green-700 mb-2"><strong>Token received:</strong></p>
      <code class="block bg-green-100 p-2 rounded text-xs break-all">{token}</code>
    </div>
  {/if}

  {#if verificationResult}
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
      <h2 class="text-xl font-semibold text-blue-800 mb-4">Result</h2>
      <pre class="bg-blue-100 p-4 rounded text-sm overflow-auto">{verificationResult}</pre>
    </div>
  {/if}
</div>
