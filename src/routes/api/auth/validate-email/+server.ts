/**
 * API Endpoint: POST /api/auth/validate-email
 * 
 * Validates email addresses for registration by:
 * 1. Normalizing Gmail dot/plus aliases
 * 2. Checking for existing users with normalized email
 * 3. Returning validation results
 */

import { json } from '@sveltejs/kit';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { normalizeEmail, validateEmailForRegistration } from '$lib/utils/email-normalization';
import { PUBLIC_NHOST_SUBDOMAIN, PUBLIC_NHOST_REGION } from '$env/static/public';
import { NHOST_ADMIN_SECRET } from '$env/static/private';

interface EmailValidationRequest {
  email: string;
  checkExisting?: boolean;
}

interface EmailValidationResponse {
  success: boolean;
  normalizedEmail: string;
  isAllowed: boolean;
  warnings: string[];
  errors: string[];
  emailChanged: boolean;
}

/**
 * Checks if normalized email already exists in nHost users table
 */
async function checkEmailExists(normalizedEmail: string): Promise<boolean> {
  const graphqlEndpoint = `https://${PUBLIC_NHOST_SUBDOMAIN}.graphql.${PUBLIC_NHOST_REGION}.nhost.run/v1`;
  
  const query = `
    query CheckEmailExists($email: String!) {
      authUsers(where: { email: { _eq: $email } }) {
        id
        email
      }
    }
  `;

  try {
    const response = await fetch(graphqlEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-hasura-admin-secret': NHOST_ADMIN_SECRET
      },
      body: JSON.stringify({
        query,
        variables: { email: normalizedEmail }
      })
    });

    const result = await response.json();
    
    if (result.errors) {
      console.error('GraphQL Error checking email:', result.errors);
      return false; // Allow registration if check fails
    }

    return result.data?.authUsers?.length > 0;
  } catch (error) {
    console.error('Error checking email existence:', error);
    return false; // Allow registration if check fails
  }
}

export const POST: RequestHandler = async ({ request }) => {
  try {
    const body: EmailValidationRequest = await request.json();
    
    if (!body.email) {
      return json({
        success: false,
        errors: ['Email is required'],
        isAllowed: false,
        normalizedEmail: '',
        warnings: [],
        emailChanged: false
      } as EmailValidationResponse, { status: 400 });
    }

    // Validate and normalize email
    const validation = validateEmailForRegistration(body.email);
    const emailChanged = body.email.toLowerCase() !== validation.normalizedEmail;

    // Check if email already exists (if requested)
    let emailExists = false;
    if (body.checkExisting) {
      emailExists = await checkEmailExists(validation.normalizedEmail);
    }

    const response: EmailValidationResponse = {
      success: true,
      normalizedEmail: validation.normalizedEmail,
      isAllowed: !emailExists,
      warnings: validation.warnings,
      errors: emailExists ? ['An account with this email already exists'] : [],
      emailChanged
    };

    return json(response);

  } catch (error) {
    console.error('Email validation error:', error);
    
    return json({
      success: false,
      errors: [error instanceof Error ? error.message : 'Invalid email format'],
      isAllowed: false,
      normalizedEmail: '',
      warnings: [],
      emailChanged: false
    } as EmailValidationResponse, { status: 400 });
  }
};
