import type { Handle } from '@sveltejs/kit';
import { handlePreClearance } from '$lib/middleware/turnstile';

export const handle: Handle = async ({ event, resolve }) => {
  // Handle Turnstile pre-clearance for protected routes
  const preClearanceResponse = await handlePreClearance(event);
  if (preClearanceResponse) {
    return preClearanceResponse;
  }

  const response = await resolve(event);

  // Security headers with Turnstile support
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');

  // Enhanced CSP for nHost + Turnstile pre-clearance
  response.headers.set('Content-Security-Policy', [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://challenges.cloudflare.com",
    "style-src 'self' 'unsafe-inline'",
    "connect-src 'self' https://*.nhost.run https://*.graphql.us-west-2.nhost.run https://challenges.cloudflare.com",
    "frame-src https://challenges.cloudflare.com",
    "img-src 'self' data: https:",
    "font-src 'self' https:"
  ].join('; '));

  return response;
};
