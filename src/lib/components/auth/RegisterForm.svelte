<script lang="ts">
  import { goto } from '$app/navigation';
  import { authActions, isAuthenticated } from '$lib/stores/auth.js';
  import { onMount } from 'svelte';
  import toast from 'svelte-5-french-toast';
  import { normalizeEmail, validateEmailForRegistration } from '$lib/utils/email-normalization.js';
  
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';

  let email = $state('');
  let password = $state('');
  let confirmPassword = $state('');
  let companyName = $state('');
  let showPassword = $state(false);
  let showConfirmPassword = $state(false);
  let isLoading = $state(false);

  // Email validation states
  let normalizedEmail = $state('');
  let emailWarnings = $state<string[]>([]);
  let emailValidated = $state(false);
  let isCheckingEmail = $state(false);

  // Real-time validation states
  let emailError = $state('');
  let passwordError = $state('');
  let confirmPasswordError = $state('');
  let isFormValid = $state(false);

  // Real-time validation effects
  $effect(() => {
    if (email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        emailError = 'Please enter a valid email address';
        emailValidated = false;
        normalizedEmail = '';
        emailWarnings = [];
      } else {
        emailError = '';
        validateEmailAddress(email);
      }
    } else {
      emailError = '';
      emailValidated = false;
      normalizedEmail = '';
      emailWarnings = [];
    }
  });

  // Validate and normalize email address
  async function validateEmailAddress(emailAddress: string) {
    try {
      isCheckingEmail = true;
      
      // First, do client-side validation and normalization
      const validation = validateEmailForRegistration(emailAddress);
      normalizedEmail = validation.normalizedEmail;
      emailWarnings = validation.warnings;
      
      // Check if email already exists via API
      const response = await fetch('/api/auth/validate-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: validation.normalizedEmail,
          checkExisting: true
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        if (!result.isAllowed) {
          emailError = result.errors?.[0] || 'This email is not available';
          emailValidated = false;
        } else {
          emailValidated = true;
        }
      } else {
        // If API fails, still allow registration but warn user
        emailValidated = true;
        console.warn('Email validation API failed, proceeding with registration');
      }
    } catch (error) {
      // If validation fails, still allow registration
      emailValidated = true;
      console.warn('Email validation failed:', error);
    } finally {
      isCheckingEmail = false;
    }
  }

  $effect(() => {
    if (password) {
      if (password.length < 8) {
        passwordError = 'Password must be at least 8 characters long';
      } else if (!/(?=.*[a-z])/.test(password)) {
        passwordError = 'Password must contain at least one lowercase letter';
      } else if (!/(?=.*[A-Z])/.test(password)) {
        passwordError = 'Password must contain at least one uppercase letter';
      } else if (!/(?=.*\d)/.test(password)) {
        passwordError = 'Password must contain at least one number';
      } else {
        passwordError = '';
      }
    } else {
      passwordError = '';
    }
  });

  $effect(() => {
    if (confirmPassword) {
      confirmPasswordError = password !== confirmPassword ? 'Passwords do not match' : '';
    } else {
      confirmPasswordError = '';
    }
  });

  $effect(() => {
    isFormValid = !!email && 
                  !!password && 
                  !!confirmPassword && 
                  !emailError && 
                  !passwordError && 
                  !confirmPasswordError &&
                  emailValidated &&
                  !isCheckingEmail;
  });

  onMount(() => {
    const unsubscribe = isAuthenticated.subscribe((authenticated) => {
      if (authenticated) {
        goto('/dashboard');
      }
    });
    return unsubscribe;
  });

  function extractEmailDomain(email: string): string {
    const domain = email.split('@')[1];
    if (!domain) return '';
    const parts = domain.split('.');
    if (parts.length > 2) {
      return parts.slice(-2).join('.');
    }
    return domain;
  }

  async function handleSubmit(event: Event) {
    event.preventDefault();
    
    if (!isFormValid) {
      toast.error('Please fix the form errors before submitting');
      return;
    }

    isLoading = true;

    try {
      // Use normalized email for registration
      const emailToUse = normalizedEmail || email;
      const emailDomain = extractEmailDomain(emailToUse);
      
      const result = await authActions.signUp(emailToUse, password, {
        metadata: {
          company_name: companyName || null,
          email_domain: emailDomain
        }
      });
      
      if (result.error) {
        toast.error(result.error.message || 'Registration failed');
        return;
      }

      toast.success('Registration successful! Please check your email to verify your account.');
      goto('/auth/login');
      
    } catch (error) {
      console.error('Registration error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      isLoading = false;
    }
  }
</script>

<svelte:head>
  <title>Register - SourceFlex</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background py-12 px-4">
  <div class="w-full max-w-md space-y-8">
    <div class="text-center">
      <h2 class="text-3xl font-bold">Create your account</h2>
    </div>

    <form class="space-y-4" onsubmit={handleSubmit}>
      <div class="space-y-2">
        <label for="email" class="text-sm font-medium">Email address *</label>
        <div class="relative">
          <Input
            id="email"
            type="email"
            bind:value={email}
            placeholder="Enter your email"
            disabled={isLoading}
            class={emailError ? 'border-red-500' : emailValidated ? 'border-green-500' : ''}
          />
          {#if isCheckingEmail}
            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
              <svg class="animate-spin h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
          {/if}
        </div>
        {#if emailError}
          <p class="text-sm text-red-600">{emailError}</p>
        {/if}
        {#if normalizedEmail && normalizedEmail !== email.toLowerCase()}
          <p class="text-sm text-blue-600">Email normalized to: {normalizedEmail}</p>
        {/if}
        {#if emailWarnings.length > 0}
          {#each emailWarnings as warning}
            <p class="text-sm text-amber-600">{warning}</p>
          {/each}
        {/if}
        {#if emailValidated && !isCheckingEmail}
          <p class="text-sm text-green-600">✓ Email is available</p>
        {/if}
      </div>

      <div class="space-y-2">
        <label for="company" class="text-sm font-medium">Company name (optional)</label>
        <Input
          id="company"
          type="text"
          bind:value={companyName}
          placeholder="Your company name"
          disabled={isLoading}
        />
      </div>

      <div class="space-y-2">
        <label for="password" class="text-sm font-medium">Password *</label>
        <div class="relative">
          <Input
            id="password"
            type={showPassword ? 'text' : 'password'}
            bind:value={password}
            placeholder="Choose a password"
            disabled={isLoading}
            class="{passwordError ? 'border-red-500' : ''} pr-10"
          />
          <button
            type="button"
            class="absolute inset-y-0 right-0 flex items-center pr-3"
            onclick={() => showPassword = !showPassword}
          >
            {#if showPassword}
              <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M14.12 14.12l1.415 1.415M14.12 14.12L9.878 9.878m4.242 4.242L19.5 19.5m-15-15l15 15" />
              </svg>
            {:else}
              <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            {/if}
          </button>
        </div>
        {#if passwordError}
          <p class="text-sm text-red-600">{passwordError}</p>
        {:else}
          <p class="text-xs text-muted-foreground">
            Must be at least 8 characters with uppercase, lowercase, and number
          </p>
        {/if}
      </div>

      <div class="space-y-2">
        <label for="confirm-password" class="text-sm font-medium">Confirm Password *</label>
        <div class="relative">
          <Input
            id="confirm-password"
            type={showConfirmPassword ? 'text' : 'password'}
            bind:value={confirmPassword}
            placeholder="Confirm your password"
            disabled={isLoading}
            class="{confirmPasswordError ? 'border-red-500' : ''} pr-10"
          />
          <button
            type="button"
            class="absolute inset-y-0 right-0 flex items-center pr-3"
            onclick={() => showConfirmPassword = !showConfirmPassword}
          >
            {#if showConfirmPassword}
              <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M14.12 14.12l1.415 1.415M14.12 14.12L9.878 9.878m4.242 4.242L19.5 19.5m-15-15l15 15" />
              </svg>
            {:else}
              <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            {/if}
          </button>
        </div>
        {#if confirmPasswordError}
          <p class="text-sm text-red-600">{confirmPasswordError}</p>
        {/if}
      </div>

      <Button type="submit" class="w-full" disabled={isLoading || !isFormValid}>
        {#if isLoading}
          <svg class="animate-spin -ml-1 mr-3 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Creating account...
        {:else}
          Create account
        {/if}
      </Button>

      <div class="text-center">
        <p class="text-sm text-muted-foreground">
          Already have an account?
          <a href="/auth/login" class="font-medium text-primary hover:underline">
            Sign in to your existing account
          </a>
        </p>
      </div>

      <div class="text-xs text-muted-foreground text-center">
        By creating an account, you agree to our Terms of Service and Privacy Policy.
      </div>
    </form>
  </div>
</div>
