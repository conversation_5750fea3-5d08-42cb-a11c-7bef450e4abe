<script lang="ts">
import { onMount } from 'svelte';
import { browser } from '$app/environment';

export let onSuccess = (token: string) => {};
export let onError = (error: string) => {};
export let onExpired = () => {};
export let theme: 'light' | 'dark' | 'auto' = 'auto';
export let size: 'normal' | 'compact' = 'normal';
export let retry: 'auto' | 'never' = 'auto';

let turnstileContainer: HTMLDivElement;
let widgetId: string | null = null;
let isLoaded = false;

const SITE_KEY = import.meta.env.PUBLIC_TURNSTILE_SITE_KEY;

// Load Turnstile script
const loadTurnstile = () => {
  return new Promise<void>((resolve, reject) => {
    if (typeof window.turnstile !== 'undefined') {
      resolve();
      return;
    }

    // Check if script is already in DOM
    const existingScript = document.querySelector('script[src*="turnstile"]');
    if (existingScript) {
      const checkTurnstile = () => {
        if (typeof window.turnstile !== 'undefined') {
          resolve();
        } else {
          setTimeout(checkTurnstile, 100);
        }
      };
      checkTurnstile();
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      // Wait for turnstile to be available with timeout
      let attempts = 0;
      const maxAttempts = 50; // 5 seconds
      const checkTurnstile = () => {
        if (typeof window.turnstile !== 'undefined') {
          resolve();
        } else if (attempts < maxAttempts) {
          attempts++;
          setTimeout(checkTurnstile, 100);
        } else {
          reject(new Error('Turnstile script loaded but API not available'));
        }
      };
      checkTurnstile();
    };
    
    script.onerror = () => reject(new Error('Failed to load Turnstile script'));
    document.head.appendChild(script);
  });
};

// Render Turnstile widget
const renderTurnstile = async () => {
  if (!browser || !turnstileContainer || !SITE_KEY) {
    console.error('Cannot render Turnstile:', { browser, container: !!turnstileContainer, siteKey: SITE_KEY });
    onError('Configuration error - missing requirements');
    return;
  }
  
  try {
    console.log('Loading Turnstile with site key:', SITE_KEY);
    await loadTurnstile();
    
    if (widgetId) {
      console.log('Removing existing widget:', widgetId);
      window.turnstile.remove(widgetId);
      widgetId = null;
    }

    console.log('Rendering Turnstile widget...');
    widgetId = window.turnstile.render(turnstileContainer, {
      sitekey: SITE_KEY,
      theme,
      size,
      retry,
      callback: (token: string) => {
        console.log('Turnstile success, token received:', token ? 'Yes' : 'No');
        onSuccess(token);
      },
      'error-callback': (error: string) => {
        console.error('Turnstile error callback:', error);
        onError(error);
      },
      'expired-callback': () => {
        console.log('Turnstile expired');
        onExpired();
      },
      'timeout-callback': () => {
        console.log('Turnstile timeout');
        onError('Verification timed out');
      }
    });
    
    console.log('Turnstile widget rendered with ID:', widgetId);
    isLoaded = true;
  } catch (error) {
    console.error('Error loading Turnstile:', error);
    onError(error instanceof Error ? error.message : 'Unknown error loading verification');
  }
};

// Reset the widget
export const reset = () => {
  if (browser && widgetId && window.turnstile) {
    window.turnstile.reset(widgetId);
  }
};

// Get response token
export const getResponse = (): string | null => {
  if (browser && widgetId && window.turnstile) {
    return window.turnstile.getResponse(widgetId);
  }
  return null;
};

onMount(() => {
  if (!SITE_KEY) {
    console.error('Turnstile site key not found. Please set PUBLIC_TURNSTILE_SITE_KEY in your environment variables.');
    return;
  }
  
  renderTurnstile();
  
  return () => {
    if (browser && widgetId && window.turnstile) {
      window.turnstile.remove(widgetId);
    }
  };
});

// Global type declaration for TypeScript
declare global {
  interface Window {
    turnstile: {
      render: (container: HTMLElement, options: any) => string;
      reset: (widgetId: string) => void;
      remove: (widgetId: string) => void;
      getResponse: (widgetId: string) => string | null;
    };
  }
}
</script>

<div bind:this={turnstileContainer} class="turnstile-widget"></div>

<style>
  .turnstile-widget {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 65px;
  }
</style>
