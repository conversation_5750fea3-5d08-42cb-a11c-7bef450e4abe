/**
 * Email Normalization Utilities for SourceFlex
 * 
 * Prevents Gmail dot/plus alias exploitation by normalizing email addresses
 * to their canonical form before user registration.
 * 
 * Security Goals:
 * - Prevent multiple account creation with same Gmail using dots
 * - Prevent plus alias exploitation (<EMAIL>)
 * - Handle Google domain variations (gmail.com, googlemail.com)
 * - Maintain compatibility with other email providers
 */

/**
 * Gmail domains that support dot/plus aliasing
 */
const GMAIL_DOMAINS = [
  'gmail.com',
  'googlemail.com', 
  'google.com'
] as const;

/**
 * Other providers that support plus aliasing
 */
const PLUS_ALIAS_DOMAINS = [
  'outlook.com',
  'hotmail.com', 
  'live.com',
  'fastmail.com',
  'protonmail.com',
  'yahoo.com'
] as const;

/**
 * Checks if an email domain is Gmail or Gmail-equivalent
 */
export function isGmailDomain(domain: string): boolean {
  return GMAIL_DOMAINS.includes(domain.toLowerCase() as any);
}

/**
 * Checks if an email domain supports plus aliasing
 */
export function supportsPlusAliasing(domain: string): boolean {
  const lowerDomain = domain.toLowerCase();
  return isGmailDomain(lowerDomain) || PLUS_ALIAS_DOMAINS.includes(lowerDomain as any);
}

/**
 * Normalizes Gmail addresses by:
 * 1. Converting to lowercase
 * 2. Removing all dots from local part
 * 3. Removing plus aliases (+anything)
 * 4. Converting googlemail.com/google.com to gmail.com
 * 
 * Examples:
 * - <EMAIL> → <EMAIL>
 * - <EMAIL> → <EMAIL>
 * - <EMAIL> → <EMAIL>
 */
export function normalizeGmailAddress(email: string): string {
  const [localPart, domain] = email.toLowerCase().split('@');
  
  if (!domain || !isGmailDomain(domain)) {
    return email.toLowerCase();
  }
  
  // Remove plus aliases (everything after +)
  const withoutPlus = localPart.split('+')[0];
  
  // Remove all dots from local part
  const withoutDots = withoutPlus.replace(/\./g, '');
  
  // Normalize domain to gmail.com
  return `${withoutDots}@gmail.com`;
}

/**
 * Normalizes email addresses for other providers that support plus aliasing
 * Only removes plus aliases, keeps dots as they may be significant
 */
export function normalizeOtherProviders(email: string): string {
  const [localPart, domain] = email.toLowerCase().split('@');
  
  if (!domain || !supportsPlusAliasing(domain)) {
    return email.toLowerCase();
  }
  
  // Only remove plus aliases for non-Gmail providers
  const withoutPlus = localPart.split('+')[0];
  return `${withoutPlus}@${domain}`;
}

/**
 * Main email normalization function
 * Applies appropriate normalization based on email provider
 * 
 * @param email - Email address to normalize
 * @returns Normalized email address
 */
export function normalizeEmail(email: string): string {
  if (!email || !email.includes('@')) {
    throw new Error('Invalid email address format');
  }
  
  const [, domain] = email.toLowerCase().split('@');
  
  if (isGmailDomain(domain)) {
    return normalizeGmailAddress(email);
  } else if (supportsPlusAliasing(domain)) {
    return normalizeOtherProviders(email);
  } else {
    return email.toLowerCase();
  }
}

/**
 * Validates if an email has potential aliases
 * Useful for warning users about email normalization
 */
export function hasEmailAliases(email: string): {
  hasDots: boolean;
  hasPlus: boolean;
  isGmail: boolean;
  originalEmail: string;
  normalizedEmail: string;
} {
  const [localPart, domain] = email.toLowerCase().split('@');
  const isGmail = isGmailDomain(domain);
  
  return {
    hasDots: localPart.includes('.'),
    hasPlus: localPart.includes('+'),
    isGmail,
    originalEmail: email,
    normalizedEmail: normalizeEmail(email)
  };
}

/**
 * Checks if two emails are the same after normalization
 * Useful for duplicate detection
 */
export function areEmailsEquivalent(email1: string, email2: string): boolean {
  try {
    return normalizeEmail(email1) === normalizeEmail(email2);
  } catch {
    return false;
  }
}

/**
 * Security validation for email registration
 * Returns normalized email or throws error if blocked
 */
export function validateEmailForRegistration(email: string): {
  normalizedEmail: string;
  warnings: string[];
} {
  const warnings: string[] = [];
  
  try {
    const normalizedEmail = normalizeEmail(email);
    const aliases = hasEmailAliases(email);
    
    // Add warnings for user awareness
    if (aliases.isGmail && aliases.hasDots) {
      warnings.push('Gmail addresses with dots are normalized (dots are ignored)');
    }
    
    if (aliases.hasPlus) {
      warnings.push('Plus aliases (+text) are removed from email addresses');
    }
    
    if (aliases.originalEmail !== aliases.normalizedEmail) {
      warnings.push(`Email normalized to: ${normalizedEmail}`);
    }
    
    return {
      normalizedEmail,
      warnings
    };
  } catch (error) {
    throw new Error(`Invalid email format: ${email}`);
  }
}
